<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test RajaUpload Field</title>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Alpine.js -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    
    <style>
        [x-cloak] { display: none !important; }
    </style>
</head>
<body class="bg-gray-100 min-h-screen py-8">
    <div class="max-w-4xl mx-auto px-4">
        <div class="bg-white rounded-lg shadow-lg p-8">
            <h1 class="text-3xl font-bold text-gray-800 mb-8 text-center">
                🖼️ Test RajaUpload Field
            </h1>
            
            <div class="space-y-8">
                <!-- Basic Upload Test -->
                <div class="border border-gray-200 rounded-lg p-6">
                    <h2 class="text-xl font-semibold text-gray-700 mb-4">
                        📤 Basic Upload Test
                    </h2>
                    <div class="space-y-4">
                        <div id="basic-upload-container">
                            <!-- Basic Upload Test -->
                            <div class="raja-upload-container" x-data="rajaUpload({
                                statePath: 'basic_image',
                                isMultiple: false,
                                collection: 'default',
                                maxFileSize: 10,
                                acceptedTypes: 'image/jpeg,image/png,image/webp',
                                previewSize: 150,
                                placeholder: 'Klik untuk upload gambar...',
                                showFileName: true,
                                showFileSize: true,
                                showRemoveButton: true,
                                dragDropEnabled: true,
                                convertWebp: true,
                                generateThumbnails: true,
                                theme: 'default',
                                uploadUrl: '/api/raja-upload/upload',
                                showUrl: '/api/raja-upload/',
                                deleteUrl: '/api/raja-upload/',
                                csrfToken: '{{ csrf_token() }}'
                            })">
                                <div class="upload-area border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-400 transition-colors cursor-pointer"
                                     :class="{ 'border-blue-400 bg-blue-50': isDragOver }"
                                     @click="$refs.fileInput.click()"
                                     @dragover.prevent="isDragOver = true"
                                     @dragleave.prevent="isDragOver = false"
                                     @drop.prevent="handleDrop($event)">

                                    <input type="file"
                                           x-ref="fileInput"
                                           class="hidden"
                                           :accept="acceptedTypes"
                                           :multiple="isMultiple"
                                           @change="handleFileSelect($event)">

                                    <div class="upload-icon mb-4">
                                        <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                            <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                        </svg>
                                    </div>

                                    <p class="text-gray-600 mb-2" x-text="placeholder"></p>
                                    <p class="text-sm text-gray-400">Drag & drop atau klik untuk memilih file</p>
                                </div>

                                <!-- File Preview -->
                                <div x-show="files.length > 0" class="mt-4 space-y-2">
                                    <template x-for="(file, index) in files" :key="index">
                                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                            <div class="flex items-center space-x-3">
                                                <img x-show="file.preview" :src="file.preview" class="w-12 h-12 object-cover rounded">
                                                <div>
                                                    <p class="text-sm font-medium text-gray-900" x-text="file.name"></p>
                                                    <p class="text-xs text-gray-500" x-text="formatFileSize(file.size)"></p>
                                                </div>
                                            </div>
                                            <button @click="removeFile(index)" class="text-red-500 hover:text-red-700">
                                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                                </svg>
                                            </button>
                                        </div>
                                    </template>
                                </div>

                                <!-- Progress Bar -->
                                <div x-show="isUploading" class="mt-4">
                                    <div class="bg-gray-200 rounded-full h-2">
                                        <div class="bg-blue-600 h-2 rounded-full transition-all duration-300" :style="`width: ${uploadProgress}%`"></div>
                                    </div>
                                    <p class="text-sm text-gray-600 mt-1" x-text="`Uploading... ${uploadProgress}%`"></p>
                                </div>

                                <!-- Error Messages -->
                                <div x-show="errors.length > 0" class="mt-4">
                                    <template x-for="error in errors" :key="error">
                                        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-2">
                                            <span x-text="error"></span>
                                        </div>
                                    </template>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Multiple Upload Test -->
                <div class="border border-gray-200 rounded-lg p-6">
                    <h2 class="text-xl font-semibold text-gray-700 mb-4">
                        📁 Multiple Upload Test
                    </h2>
                    <div class="space-y-4">
                        <div id="multiple-upload-container">
                            <!-- Multiple Upload Test -->
                            <div class="raja-upload-container" x-data="rajaUpload({
                                statePath: 'gallery_images',
                                isMultiple: true,
                                collection: 'gallery',
                                maxFileSize: 5,
                                acceptedTypes: 'image/jpeg,image/png,image/webp',
                                previewSize: 120,
                                placeholder: 'Upload beberapa foto sekaligus...',
                                showFileName: true,
                                showFileSize: true,
                                showRemoveButton: true,
                                dragDropEnabled: true,
                                convertWebp: true,
                                generateThumbnails: true,
                                theme: 'compact',
                                uploadUrl: '/api/raja-upload/upload',
                                showUrl: '/api/raja-upload/',
                                deleteUrl: '/api/raja-upload/',
                                csrfToken: '{{ csrf_token() }}'
                            })">
                                <div class="upload-area border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-blue-400 transition-colors cursor-pointer"
                                     :class="{ 'border-blue-400 bg-blue-50': isDragOver }"
                                     @click="$refs.fileInputMultiple.click()"
                                     @dragover.prevent="isDragOver = true"
                                     @dragleave.prevent="isDragOver = false"
                                     @drop.prevent="handleDrop($event)">

                                    <input type="file"
                                           x-ref="fileInputMultiple"
                                           class="hidden"
                                           :accept="acceptedTypes"
                                           :multiple="isMultiple"
                                           @change="handleFileSelect($event)">

                                    <div class="upload-icon mb-2">
                                        <svg class="mx-auto h-8 w-8 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                            <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                        </svg>
                                    </div>

                                    <p class="text-gray-600 text-sm mb-1" x-text="placeholder"></p>
                                    <p class="text-xs text-gray-400">Multiple files allowed</p>
                                </div>

                                <!-- Multiple Files Preview -->
                                <div x-show="files.length > 0" class="mt-3 grid grid-cols-3 gap-2">
                                    <template x-for="(file, index) in files" :key="index">
                                        <div class="relative group">
                                            <img x-show="file.preview" :src="file.preview" class="w-full h-20 object-cover rounded border">
                                            <div class="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity rounded flex items-center justify-center">
                                                <button @click="removeFile(index)" class="text-white hover:text-red-300">
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                                    </svg>
                                                </button>
                                            </div>
                                            <div x-show="showFileSize" class="absolute bottom-0 left-0 right-0 bg-black bg-opacity-75 text-white text-xs p-1 rounded-b">
                                                <span x-text="formatFileSize(file.size)"></span>
                                            </div>
                                            <div x-show="showFileName" class="absolute top-0 left-0 right-0 bg-black bg-opacity-75 text-white text-xs p-1 rounded-t truncate">
                                                <span x-text="file.name"></span>
                                            </div>
                                        </div>
                                    </template>
                                </div>

                                <!-- Progress Bar -->
                                <div x-show="isUploading" class="mt-3">
                                    <div class="bg-gray-200 rounded-full h-1">
                                        <div class="bg-blue-600 h-1 rounded-full transition-all duration-300" :style="`width: ${uploadProgress}%`"></div>
                                    </div>
                                    <p class="text-xs text-gray-600 mt-1" x-text="`Uploading... ${uploadProgress}%`"></p>
                                </div>

                                <!-- Error Messages -->
                                <div x-show="errors.length > 0" class="mt-3">
                                    <template x-for="error in errors" :key="error">
                                        <div class="bg-red-100 border border-red-400 text-red-700 px-3 py-2 rounded text-sm mb-1">
                                            <span x-text="error"></span>
                                        </div>
                                    </template>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Large Theme Test -->
                <div class="border border-gray-200 rounded-lg p-6">
                    <h2 class="text-xl font-semibold text-gray-700 mb-4">
                        🖼️ Large Theme Test
                    </h2>
                    <div class="space-y-4">
                        <div id="large-upload-container">
                            <!-- Large Theme Test -->
                            <div class="raja-upload-container" x-data="rajaUpload({
                                statePath: 'large_image',
                                isMultiple: false,
                                collection: 'cms',
                                maxFileSize: 15,
                                acceptedTypes: 'image/jpeg,image/png,image/webp,image/gif',
                                previewSize: 200,
                                placeholder: 'Upload gambar dengan preview besar...',
                                showFileName: true,
                                showFileSize: true,
                                showRemoveButton: true,
                                dragDropEnabled: true,
                                convertWebp: false,
                                generateThumbnails: true,
                                theme: 'large',
                                uploadUrl: '/api/raja-upload/upload',
                                showUrl: '/api/raja-upload/',
                                deleteUrl: '/api/raja-upload/',
                                csrfToken: '{{ csrf_token() }}'
                            })">
                                <div class="upload-area border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-blue-400 transition-colors cursor-pointer"
                                     :class="{ 'border-blue-400 bg-blue-50': isDragOver }"
                                     @click="$refs.fileInputLarge.click()"
                                     @dragover.prevent="isDragOver = true"
                                     @dragleave.prevent="isDragOver = false"
                                     @drop.prevent="handleDrop($event)">

                                    <input type="file"
                                           x-ref="fileInputLarge"
                                           class="hidden"
                                           :accept="acceptedTypes"
                                           :multiple="isMultiple"
                                           @change="handleFileSelect($event)">

                                    <div class="upload-icon mb-6">
                                        <svg class="mx-auto h-16 w-16 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                            <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                        </svg>
                                    </div>

                                    <p class="text-gray-600 text-lg mb-2" x-text="placeholder"></p>
                                    <p class="text-sm text-gray-400">Drag & drop atau klik untuk memilih file</p>
                                    <p class="text-xs text-gray-400 mt-2">Max 15MB • JPEG, PNG, WebP, GIF</p>
                                </div>

                                <!-- Large File Preview -->
                                <div x-show="files.length > 0" class="mt-6">
                                    <template x-for="(file, index) in files" :key="index">
                                        <div class="flex items-start space-x-4 p-4 bg-gray-50 rounded-lg">
                                            <img x-show="file.preview" :src="file.preview" class="w-32 h-32 object-cover rounded-lg border">
                                            <div class="flex-1">
                                                <div class="flex items-center justify-between">
                                                    <h4 class="text-lg font-medium text-gray-900" x-text="file.name"></h4>
                                                    <button @click="removeFile(index)" class="text-red-500 hover:text-red-700">
                                                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                                        </svg>
                                                    </button>
                                                </div>
                                                <p class="text-sm text-gray-500 mt-1">
                                                    <span x-text="formatFileSize(file.size)"></span> •
                                                    <span x-text="file.type"></span>
                                                </p>
                                                <div x-show="file.uploaded" class="mt-2">
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                        ✓ Upload berhasil
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </template>
                                </div>

                                <!-- Progress Bar -->
                                <div x-show="isUploading" class="mt-4">
                                    <div class="bg-gray-200 rounded-full h-3">
                                        <div class="bg-blue-600 h-3 rounded-full transition-all duration-300" :style="`width: ${uploadProgress}%`"></div>
                                    </div>
                                    <p class="text-sm text-gray-600 mt-2" x-text="`Uploading... ${uploadProgress}%`"></p>
                                </div>

                                <!-- Error Messages -->
                                <div x-show="errors.length > 0" class="mt-4">
                                    <template x-for="error in errors" :key="error">
                                        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-2">
                                            <span x-text="error"></span>
                                        </div>
                                    </template>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- API Test Section -->
                <div class="border border-gray-200 rounded-lg p-6">
                    <h2 class="text-xl font-semibold text-gray-700 mb-4">
                        🔧 API Test
                    </h2>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <button 
                            onclick="testUploadAPI()" 
                            class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors"
                        >
                            Test Upload API
                        </button>
                        <button 
                            onclick="testShowAPI()" 
                            class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition-colors"
                        >
                            Test Show API
                        </button>
                        <button 
                            onclick="testDeleteAPI()" 
                            class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors"
                        >
                            Test Delete API
                        </button>
                    </div>
                    <div id="api-results" class="mt-4 p-4 bg-gray-50 rounded-lg hidden">
                        <h3 class="font-semibold text-gray-700 mb-2">API Results:</h3>
                        <pre id="api-output" class="text-sm text-gray-600 overflow-auto"></pre>
                    </div>
                </div>

                <!-- Configuration Info -->
                <div class="border border-gray-200 rounded-lg p-6">
                    <h2 class="text-xl font-semibold text-gray-700 mb-4">
                        ⚙️ Configuration Info
                    </h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h3 class="font-semibold text-gray-600 mb-2">API Endpoints:</h3>
                            <ul class="text-sm text-gray-600 space-y-1">
                                <li>• POST /api/raja-upload/upload</li>
                                <li>• GET /api/raja-upload/{id}</li>
                                <li>• DELETE /api/raja-upload/{id}</li>
                            </ul>
                        </div>
                        <div>
                            <h3 class="font-semibold text-gray-600 mb-2">Supported Features:</h3>
                            <ul class="text-sm text-gray-600 space-y-1">
                                <li>• ✅ Drag & Drop Upload</li>
                                <li>• ✅ Multiple File Upload</li>
                                <li>• ✅ WebP Conversion</li>
                                <li>• ✅ Thumbnail Generation</li>
                                <li>• ✅ File Size Validation</li>
                                <li>• ✅ File Type Validation</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Alpine.js RajaUpload Component
        function rajaUpload(config) {
            return {
                // Configuration
                statePath: config.statePath || '',
                isMultiple: config.isMultiple || false,
                collection: config.collection || 'default',
                maxFileSize: config.maxFileSize || 10,
                acceptedTypes: config.acceptedTypes || 'image/*',
                previewSize: config.previewSize || 150,
                placeholder: config.placeholder || 'Klik untuk upload...',
                showFileName: config.showFileName !== false,
                showFileSize: config.showFileSize !== false,
                showRemoveButton: config.showRemoveButton !== false,
                dragDropEnabled: config.dragDropEnabled !== false,
                convertWebp: config.convertWebp !== false,
                generateThumbnails: config.generateThumbnails !== false,
                theme: config.theme || 'default',
                uploadUrl: config.uploadUrl || '/api/raja-upload/upload',
                showUrl: config.showUrl || '/api/raja-upload/',
                deleteUrl: config.deleteUrl || '/api/raja-upload/',
                csrfToken: config.csrfToken || '',

                // State
                files: [],
                isDragOver: false,
                isUploading: false,
                uploadProgress: 0,
                errors: [],

                // Methods
                handleFileSelect(event) {
                    const files = Array.from(event.target.files);
                    this.processFiles(files);
                },

                handleDrop(event) {
                    this.isDragOver = false;
                    const files = Array.from(event.dataTransfer.files);
                    this.processFiles(files);
                },

                processFiles(files) {
                    this.errors = [];

                    files.forEach(file => {
                        if (this.validateFile(file)) {
                            this.addFile(file);
                        }
                    });
                },

                validateFile(file) {
                    // Check file type
                    const acceptedTypes = this.acceptedTypes.split(',').map(type => type.trim());
                    if (!acceptedTypes.includes(file.type) && !acceptedTypes.includes('*/*')) {
                        this.errors.push(`File ${file.name}: Tipe file tidak didukung`);
                        return false;
                    }

                    // Check file size (convert MB to bytes)
                    const maxSizeBytes = this.maxFileSize * 1024 * 1024;
                    if (file.size > maxSizeBytes) {
                        this.errors.push(`File ${file.name}: Ukuran file terlalu besar (max ${this.maxFileSize}MB)`);
                        return false;
                    }

                    return true;
                },

                addFile(file) {
                    const fileObj = {
                        file: file,
                        name: file.name,
                        size: file.size,
                        type: file.type,
                        preview: null,
                        uploaded: false,
                        id: null
                    };

                    // Create preview for images
                    if (file.type.startsWith('image/')) {
                        const reader = new FileReader();
                        reader.onload = (e) => {
                            fileObj.preview = e.target.result;
                        };
                        reader.readAsDataURL(file);
                    }

                    if (this.isMultiple) {
                        this.files.push(fileObj);
                    } else {
                        this.files = [fileObj];
                    }

                    // Auto upload
                    this.uploadFile(fileObj);
                },

                async uploadFile(fileObj) {
                    this.isUploading = true;
                    this.uploadProgress = 0;

                    const formData = new FormData();
                    formData.append('file', fileObj.file);
                    formData.append('collection', this.collection);

                    try {
                        const response = await fetch(this.uploadUrl, {
                            method: 'POST',
                            body: formData,
                            headers: {
                                'X-CSRF-TOKEN': this.csrfToken
                            }
                        });

                        const result = await response.json();

                        if (result.success) {
                            fileObj.uploaded = true;
                            fileObj.id = result.data.id;
                            this.uploadProgress = 100;
                            console.log('Upload berhasil:', result);
                        } else {
                            this.errors.push(`Upload gagal: ${result.message}`);
                            this.removeFileObj(fileObj);
                        }
                    } catch (error) {
                        this.errors.push(`Upload error: ${error.message}`);
                        this.removeFileObj(fileObj);
                    } finally {
                        this.isUploading = false;
                        setTimeout(() => {
                            this.uploadProgress = 0;
                        }, 1000);
                    }
                },

                removeFile(index) {
                    const fileObj = this.files[index];
                    if (fileObj.uploaded && fileObj.id) {
                        this.deleteUploadedFile(fileObj.id);
                    }
                    this.files.splice(index, 1);
                },

                removeFileObj(fileObj) {
                    const index = this.files.indexOf(fileObj);
                    if (index > -1) {
                        this.files.splice(index, 1);
                    }
                },

                async deleteUploadedFile(fileId) {
                    try {
                        const response = await fetch(`${this.deleteUrl}${fileId}`, {
                            method: 'DELETE',
                            headers: {
                                'X-CSRF-TOKEN': this.csrfToken
                            }
                        });

                        const result = await response.json();
                        if (result.success) {
                            console.log('File berhasil dihapus:', result);
                        } else {
                            console.error('Gagal menghapus file:', result.message);
                        }
                    } catch (error) {
                        console.error('Error menghapus file:', error);
                    }
                },

                formatFileSize(bytes) {
                    if (bytes === 0) return '0 Bytes';
                    const k = 1024;
                    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                    const i = Math.floor(Math.log(bytes) / Math.log(k));
                    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
                }
            };
        }

        // Test functions untuk API
        function testUploadAPI() {
            showAPIResult('Testing Upload API...', 'info');
            
            // Create a test file input
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = 'image/*';
            input.onchange = function(e) {
                const file = e.target.files[0];
                if (file) {
                    const formData = new FormData();
                    formData.append('file', file);
                    formData.append('collection', 'test');
                    
                    fetch('/api/raja-upload/upload', {
                        method: 'POST',
                        body: formData,
                        headers: {
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        showAPIResult(JSON.stringify(data, null, 2), 'success');
                    })
                    .catch(error => {
                        showAPIResult('Error: ' + error.message, 'error');
                    });
                }
            };
            input.click();
        }

        function testShowAPI() {
            const mediaId = prompt('Masukkan Media ID untuk test:');
            if (mediaId) {
                showAPIResult('Testing Show API...', 'info');
                
                fetch(`/api/raja-upload/${mediaId}`)
                .then(response => response.json())
                .then(data => {
                    showAPIResult(JSON.stringify(data, null, 2), 'success');
                })
                .catch(error => {
                    showAPIResult('Error: ' + error.message, 'error');
                });
            }
        }

        function testDeleteAPI() {
            const mediaId = prompt('Masukkan Media ID untuk dihapus:');
            if (mediaId && confirm('Yakin ingin menghapus media ini?')) {
                showAPIResult('Testing Delete API...', 'info');
                
                fetch(`/api/raja-upload/${mediaId}`, {
                    method: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                })
                .then(response => response.json())
                .then(data => {
                    showAPIResult(JSON.stringify(data, null, 2), 'success');
                })
                .catch(error => {
                    showAPIResult('Error: ' + error.message, 'error');
                });
            }
        }

        function showAPIResult(result, type) {
            const resultsDiv = document.getElementById('api-results');
            const outputPre = document.getElementById('api-output');
            
            resultsDiv.classList.remove('hidden');
            outputPre.textContent = result;
            
            // Color coding based on type
            outputPre.className = 'text-sm overflow-auto ' + 
                (type === 'error' ? 'text-red-600' : 
                 type === 'success' ? 'text-green-600' : 
                 'text-blue-600');
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 RajaUpload Test Page Loaded');
            console.log('📋 Available API endpoints:');
            console.log('   - POST /api/raja-upload/upload');
            console.log('   - GET /api/raja-upload/{id}');
            console.log('   - DELETE /api/raja-upload/{id}');
        });
    </script>
</body>
</html>
