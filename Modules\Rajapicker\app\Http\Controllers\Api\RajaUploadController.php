<?php

namespace Modules\Rajapicker\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Spatie\MediaLibrary\MediaCollections\Models\Media;
use Intervention\Image\ImageManager;
use Intervention\Image\Drivers\Gd\Driver;

class RajaUploadController extends Controller
{
    protected ImageManager $imageManager;

    public function __construct()
    {
        $this->imageManager = new ImageManager(new Driver());
    }

    /**
     * Upload file dan simpan ke media library
     */
    public function upload(Request $request): JsonResponse
    {
        try {
            \Log::info('RajaUpload: Upload request received', [
                'files' => $request->hasFile('file'),
                'collection' => $request->input('collection'),
                'all_data' => $request->all()
            ]);

            // Validasi request
            $validator = $this->validateUpload($request);
            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validasi gagal',
                    'errors' => $validator->errors()
                ], 422);
            }

            $file = $request->file('file');
            $collection = $request->input('collection', 'default');
            $convertWebp = $request->boolean('convert_webp', true);
            $generateThumbnails = $request->boolean('generate_thumbnails', true);

            // Get collection config
            $collectionConfig = Config::get("rajapicker.rajaupload.collections.{$collection}", Config::get('rajapicker.rajaupload.collections.default'));

            // Process file upload
            $media = $this->processFileUpload($file, $collection, $collectionConfig, $convertWebp, $generateThumbnails);

            return response()->json([
                'success' => true,
                'message' => 'File berhasil diupload',
                'data' => [
                    'id' => $media->id,
                    'name' => $media->name,
                    'file_name' => $media->file_name,
                    'mime_type' => $media->mime_type,
                    'size' => $media->size,
                    'url' => $media->getUrl(),
                    'collection' => $media->collection_name,
                    'created_at' => $media->created_at->toISOString(),
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat upload: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get file info by ID
     */
    public function show($id): JsonResponse
    {
        try {
            // Validate that ID is numeric
            if (!is_numeric($id)) {
                return response()->json([
                    'success' => false,
                    'message' => 'ID harus berupa angka'
                ], 400);
            }

            $media = Media::findOrFail((int)$id);

            return response()->json([
                'success' => true,
                'data' => [
                    'id' => $media->id,
                    'name' => $media->name,
                    'file_name' => $media->file_name,
                    'mime_type' => $media->mime_type,
                    'size' => $media->size,
                    'url' => $media->getUrl(),
                    'collection' => $media->collection_name,
                    'created_at' => $media->created_at->toISOString(),
                    'custom_properties' => $media->custom_properties,
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'File tidak ditemukan'
            ], 404);
        }
    }

    /**
     * Delete file
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $media = Media::findOrFail($id);
            
            // Delete thumbnails if exist
            $this->deleteThumbnails($media);
            
            // Delete media
            $media->delete();

            return response()->json([
                'success' => true,
                'message' => 'File berhasil dihapus'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal menghapus file: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Validate upload request
     */
    protected function validateUpload(Request $request): \Illuminate\Validation\Validator
    {
        $collection = $request->input('collection', 'default');
        $collectionConfig = Config::get("rajapicker.rajaupload.collections.{$collection}", Config::get('rajapicker.rajaupload.collections.default'));
        
        $maxFileSize = ($collectionConfig['max_file_size'] ?? 10) * 1024; // Convert to KB
        $acceptedTypes = $collectionConfig['accepted_file_types'] ?? ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        
        // Convert MIME types to extensions for validation
        $extensions = [];
        foreach ($acceptedTypes as $mimeType) {
            switch ($mimeType) {
                case 'image/jpeg':
                    $extensions[] = 'jpeg';
                    $extensions[] = 'jpg';
                    break;
                case 'image/png':
                    $extensions[] = 'png';
                    break;
                case 'image/gif':
                    $extensions[] = 'gif';
                    break;
                case 'image/webp':
                    $extensions[] = 'webp';
                    break;
            }
        }

        return Validator::make($request->all(), [
            'file' => [
                'required',
                'file',
                'image',
                'mimes:' . implode(',', $extensions),
                'max:' . $maxFileSize
            ],
            'collection' => 'sometimes|string',
            'convert_webp' => 'sometimes|boolean',
            'generate_thumbnails' => 'sometimes|boolean',
        ], [
            'file.required' => 'File harus dipilih',
            'file.file' => 'File yang diupload harus berupa file yang valid',
            'file.image' => 'File harus berupa gambar',
            'file.mimes' => 'Format file tidak didukung. Format yang diizinkan: ' . implode(', ', $extensions),
            'file.max' => 'Ukuran file terlalu besar. Maksimal ' . ($maxFileSize / 1024) . 'MB',
        ]);
    }

    /**
     * Process file upload
     */
    protected function processFileUpload($file, string $collection, array $collectionConfig, bool $convertWebp, bool $generateThumbnails): Media
    {
        // Generate unique filename
        $filename = $this->generateFilename($file);
        
        // Convert to WebP if enabled
        if ($convertWebp && $this->shouldConvertToWebp($file)) {
            $file = $this->convertToWebp($file, $filename);
        }

        // Create media record directly
        $media = new Media();

        // Store file
        $storagePath = Storage::disk('public')->putFileAs(
            $collectionConfig['directory'] ?? $collection,
            $file,
            $filename
        );

        // Set media properties
        $media->model_type = 'temp_model';
        $media->model_id = 0;
        $media->collection_name = $collection;
        $media->name = pathinfo($filename, PATHINFO_FILENAME);
        $media->file_name = $filename;
        $media->mime_type = $file->getMimeType();
        $media->disk = 'public';
        $media->size = $file->getSize();
        $media->manipulations = [];
        $media->custom_properties = [];
        $media->generated_conversions = [];
        $media->responsive_images = [];
        $media->save();

        // Set user_id if authenticated
        if (Auth::check()) {
            $media->update(['user_id' => Auth::id()]);
        }

        // Generate thumbnails if enabled
        if ($generateThumbnails) {
            $this->generateThumbnails($media, $collectionConfig);
        }

        return $media;
    }

    /**
     * Generate unique filename
     */
    protected function generateFilename($file): string
    {
        $config = Config::get('rajapicker.rajaupload.file_naming', []);
        
        $originalName = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
        $extension = $file->getClientOriginalExtension();
        
        // Clean filename
        $name = Str::slug($originalName);
        if ($config['lowercase'] ?? true) {
            $name = strtolower($name);
        }
        
        $separator = $config['separator'] ?? '_';
        $pattern = $config['pattern'] ?? '{name}{sep}{timestamp}{sep}{random}';
        
        // Replace placeholders
        $filename = str_replace([
            '{name}',
            '{timestamp}',
            '{random}',
            '{sep}'
        ], [
            $name,
            $config['append_timestamp'] ?? true ? time() : '',
            ($config['append_random_length'] ?? 8) > 0 ? Str::random($config['append_random_length'] ?? 8) : '',
            $separator
        ], $pattern);
        
        // Clean up multiple separators
        $filename = preg_replace('/[' . preg_quote($separator) . ']+/', $separator, $filename);
        $filename = trim($filename, $separator);
        
        // Limit length
        $maxLength = ($config['max_length'] ?? 100) - strlen($extension) - 1;
        if (strlen($filename) > $maxLength) {
            $filename = substr($filename, 0, $maxLength);
        }
        
        return $filename . '.' . $extension;
    }

    /**
     * Check if file should be converted to WebP
     */
    protected function shouldConvertToWebp($file): bool
    {
        $mimeType = $file->getMimeType();
        return in_array($mimeType, ['image/jpeg', 'image/png']) && 
               Config::get('rajaupload.storage.webp.enabled', true);
    }

    /**
     * Convert image to WebP
     */
    protected function convertToWebp($file, string $filename)
    {
        $image = $this->imageManager->read($file->getPathname());
        
        $quality = Config::get('rajaupload.storage.webp.quality', 85);
        $webpData = $image->toWebp($quality);
        
        // Create temporary file
        $tempPath = sys_get_temp_dir() . '/' . pathinfo($filename, PATHINFO_FILENAME) . '.webp';
        file_put_contents($tempPath, $webpData);
        
        return new \Illuminate\Http\UploadedFile(
            $tempPath,
            pathinfo($filename, PATHINFO_FILENAME) . '.webp',
            'image/webp',
            null,
            true
        );
    }

    /**
     * Generate thumbnails for media
     */
    protected function generateThumbnails(Media $media, array $collectionConfig): void
    {
        $thumbnailConfig = Config::get('rajapicker.rajaupload.storage.thumbnail', []);
        
        if (!($thumbnailConfig['enabled'] ?? true)) {
            return;
        }

        $sizes = $thumbnailConfig['sizes'] ?? [];
        $originalPath = $media->getPath();
        
        foreach ($sizes as $sizeName => $sizeConfig) {
            if (!($sizeConfig['enabled'] ?? true)) {
                continue;
            }

            try {
                $image = $this->imageManager->read($originalPath);
                
                // Resize based on configuration
                if (isset($sizeConfig['width']) && isset($sizeConfig['height'])) {
                    $image->resize($sizeConfig['width'], $sizeConfig['height']);
                } elseif (isset($sizeConfig['percentage'])) {
                    $originalWidth = $image->width();
                    $originalHeight = $image->height();
                    $newWidth = (int)($originalWidth * $sizeConfig['percentage'] / 100);
                    $newHeight = (int)($originalHeight * $sizeConfig['percentage'] / 100);
                    $image->resize($newWidth, $newHeight);
                }
                
                // Generate thumbnail filename
                $suffix = $sizeConfig['suffix'] ?? $sizeName;
                $thumbnailFilename = pathinfo($media->file_name, PATHINFO_FILENAME) . '_' . $suffix . '.webp';
                
                // Save thumbnail
                $thumbnailPath = dirname($media->getPath()) . '/thumbnails/' . $thumbnailFilename;
                $thumbnailDir = dirname($thumbnailPath);
                
                if (!is_dir($thumbnailDir)) {
                    mkdir($thumbnailDir, 0755, true);
                }
                
                $quality = $sizeConfig['quality'] ?? 80;
                $webpData = $image->toWebp($quality);
                file_put_contents($thumbnailPath, $webpData);
                
            } catch (\Exception $e) {
                // Log error but don't fail the upload
                Log::error("Failed to generate thumbnail {$sizeName} for media {$media->id}: " . $e->getMessage());
            }
        }
    }

    /**
     * Delete thumbnails for media
     */
    protected function deleteThumbnails(Media $media): void
    {
        $thumbnailConfig = Config::get('rajapicker.rajaupload.storage.thumbnail', []);
        $sizes = $thumbnailConfig['sizes'] ?? [];
        
        foreach ($sizes as $sizeName => $sizeConfig) {
            $suffix = $sizeConfig['suffix'] ?? $sizeName;
            $thumbnailFilename = pathinfo($media->file_name, PATHINFO_FILENAME) . '_' . $suffix . '.webp';
            $thumbnailPath = dirname($media->getPath()) . '/thumbnails/' . $thumbnailFilename;
            
            if (file_exists($thumbnailPath)) {
                unlink($thumbnailPath);
            }
        }
    }
}
